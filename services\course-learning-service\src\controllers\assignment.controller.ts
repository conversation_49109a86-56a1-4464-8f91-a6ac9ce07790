import { Request, Response } from "express";
import { CourseModel } from "../models/Course.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse, AssignmentCompletedEvent } from "@shared/types";
import { rabbitMQ } from "@shared/utils/rabbitmq";
import Joi from "joi";

export class AssignmentController {
  private createAssignmentSchema = Joi.object({
    title: Joi.string().min(3).max(200).required(),
    triggerTimestamp: Joi.number().min(0).required(),
    timeLimit: Joi.number().min(30).max(1800).required(), // 30 seconds to 30 minutes
    questions: Joi.array()
      .items(
        Joi.object({
          text: Joi.string().required(),
          type: Joi.string().valid("short_answer", "essay").required(),
          correctAnswer: Joi.string().when("type", {
            is: "short_answer",
            then: Joi.required(),
            otherwise: Joi.forbidden(),
          }),
          points: Joi.number().min(1).default(1),
        })
      )
      .min(1)
      .required(),
    passingScore: Joi.number().min(0).max(100).default(100),
  });

  private submitAssignmentSchema = Joi.object({
    courseId: Joi.string().required(),
    moduleId: Joi.string().required(),
    lessonId: Joi.string().required(),
    assignmentId: Joi.string().required(),
    answers: Joi.array()
      .items(
        Joi.object({
          questionId: Joi.string().required(),
          answer: Joi.string().required(),
        })
      )
      .required(),
    timeSpent: Joi.number().min(1).required(), // seconds
  });

  async createAssignment(req: AuthenticatedRequest, res: Response) {
    try {
      const { courseId, moduleId, lessonId } = req.params;
      const { error, value } = this.createAssignmentSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const course = await CourseModel.findOne({
        _id: courseId,
        tutorId: req.user!.uid,
      });

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      const module = course.modules.find((m) => m._id!.toString() === moduleId);
      if (!module) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const lesson = module.lessons.find((l) => l._id!.toString() === lessonId);
      if (!lesson) {
        return res.status(404).json({
          success: false,
          message: "Lesson not found",
        } as ApiResponse);
      }

      lesson.assignments.push(value);
      await course.save();

      res.status(201).json({
        success: true,
        message: "Assignment created successfully",
        data: value,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Create assignment error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create assignment",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async submitAssignment(req: AuthenticatedRequest, res: Response) {
    try {
      const { error, value } = this.submitAssignmentSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const { courseId, moduleId, lessonId, assignmentId, answers, timeSpent } =
        value;

      // Find the assignment
      const course = await CourseModel.findById(courseId);
      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found",
        } as ApiResponse);
      }

      const module = course.modules.find((m) => m._id!.toString() === moduleId);
      if (!module) {
        return res.status(404).json({
          success: false,
          message: "Module not found",
        } as ApiResponse);
      }

      const lesson = module.lessons.find((l) => l._id!.toString() === lessonId);
      if (!lesson) {
        return res.status(404).json({
          success: false,
          message: "Lesson not found",
        } as ApiResponse);
      }

      const assignment = lesson.assignments.find(
        (a) => a._id!.toString() === assignmentId
      );
      if (!assignment) {
        return res.status(404).json({
          success: false,
          message: "Assignment not found",
        } as ApiResponse);
      }

      // Grade the assignment
      let totalPoints = 0;
      let earnedPoints = 0;
      const gradedAnswers = answers.map((studentAnswer: any) => {
        const question = assignment.questions.find(
          (q) => q._id!.toString() === studentAnswer.questionId
        );
        if (!question) {
          return {
            ...studentAnswer,
            isCorrect: false,
            points: 0,
          };
        }

        totalPoints += question.points;
        let isCorrect = false;
        let points = 0;

        if (question.type === "short_answer" && question.correctAnswer) {
          // Simple string comparison (case-insensitive)
          isCorrect =
            studentAnswer.answer.toLowerCase().trim() ===
            question.correctAnswer.toLowerCase().trim();
          points = isCorrect ? question.points : 0;
        } else if (question.type === "essay") {
          // For essays, we'll need manual grading or AI-assisted grading
          // For now, mark as correct (this should be improved)
          isCorrect = true;
          points = question.points;
        }

        earnedPoints += points;

        return {
          questionId: studentAnswer.questionId,
          answer: studentAnswer.answer,
          isCorrect,
          points,
        };
      });

      const percentage =
        totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
      const passed = percentage >= assignment.passingScore;

      const assignmentResult = {
        assignmentId,
        lessonId,
        score: earnedPoints,
        totalPoints,
        percentage,
        passed,
        submittedAt: new Date(),
        answers: gradedAnswers,
      };

      // Publish assignment completed event
      const event: AssignmentCompletedEvent = {
        type: "ASSIGNMENT_COMPLETED",
        data: {
          studentId: req.user!.uid,
          courseId,
          assignmentId,
          result: assignmentResult,
        },
        timestamp: new Date(),
      };

      await rabbitMQ.publishEvent(
        "learning.events",
        "assignment.completed",
        event
      );

      res.json({
        success: true,
        message: "Assignment submitted successfully",
        data: {
          result: assignmentResult,
          timeSpent,
        },
      } as ApiResponse);
    } catch (error: any) {
      console.error("Submit assignment error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to submit assignment",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getAssignmentResult(req: AuthenticatedRequest, res: Response) {
    try {
      // TODO: Get assignment result from student progress/subscription service
      res.json({
        success: true,
        message: "Assignment result retrieved successfully",
        data: {
          message:
            "This endpoint needs to query the subscription service for assignment results",
        },
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get assignment result error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve assignment result",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async updateAssignment(req: AuthenticatedRequest, res: Response) {
    try {
      // Implementation similar to createAssignment but updating existing
      res.json({
        success: true,
        message: "Assignment update functionality to be implemented",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Update assignment error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update assignment",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async deleteAssignment(req: AuthenticatedRequest, res: Response) {
    try {
      // Implementation similar to deleteLesson but for assignments
      res.json({
        success: true,
        message: "Assignment delete functionality to be implemented",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Delete assignment error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete assignment",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
