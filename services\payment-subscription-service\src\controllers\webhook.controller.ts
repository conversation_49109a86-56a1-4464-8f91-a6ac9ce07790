import { Request, Response } from "express";
import { PaymentModel } from "../models/Payment.model";
import { SubscriptionModel } from "../models/Subscription.model";
import { rabbitMQ } from "@shared/utils/rabbitmq";
import { PaymentCompletedEvent, CourseEnrollmentEvent } from "@shared/types";
import crypto from "crypto";

export class WebhookController {
  async handleXenditWebhook(req: Request, res: Response) {
    try {
      const webhookPayload = req.body;
      const webhookToken = process.env.XENDIT_WEBHOOK_TOKEN;

      // Verify webhook signature
      if (webhookToken) {
        const signature = req.headers["x-callback-token"] as string;
        if (signature !== webhookToken) {
          console.log("Invalid webhook signature");
          return res.status(401).send("Invalid signature");
        }
      }

      const eventType = webhookPayload.event;

      if (eventType === "payment.completed") {
        await this.handlePaymentCompleted(webhookPayload);
      } else if (eventType === "payment.failed") {
        await this.handlePaymentFailed(webhookPayload);
      }

      res.status(200).send("Webhook processed successfully");
    } catch (error: any) {
      console.error("Webhook processing error:", error);
      res.status(500).send("Internal server error");
    }
  }

  private async handlePaymentCompleted(webhookPayload: any) {
    try {
      const xenditPaymentId = webhookPayload.data.id;

      // Find the payment record
      const payment = await PaymentModel.findOne({ xenditPaymentId });
      if (!payment) {
        console.log("Payment not found for Xendit ID:", xenditPaymentId);
        return;
      }

      // Update payment status
      payment.status = "completed";
      payment.webhookData = webhookPayload;
      await payment.save();

      // Create subscription
      const courseDurationDays = 90; // Default 90 days - this should come from course data
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + courseDurationDays);

      const subscription = new SubscriptionModel({
        studentId: payment.studentId,
        courseId: payment.courseId,
        status: "active",
        startDate: new Date(),
        endDate,
        failureCount: 0,
        paymentId: payment._id,
      });

      await subscription.save();

      // Publish events
      const paymentCompletedEvent: PaymentCompletedEvent = {
        type: "PAYMENT_COMPLETED",
        data: {
          paymentId: payment._id!.toString(),
          studentId: payment.studentId,
          courseId: payment.courseId.toString(),
          amount: payment.amount,
        },
        timestamp: new Date(),
      };

      const courseEnrollmentEvent: CourseEnrollmentEvent = {
        type: "COURSE_ENROLLED",
        data: {
          studentId: payment.studentId,
          courseId: payment.courseId.toString(),
          subscriptionId: subscription._id!.toString(),
        },
        timestamp: new Date(),
      };

      await Promise.all([
        rabbitMQ.publishEvent(
          "payment.events",
          "payment.completed",
          paymentCompletedEvent
        ),
        rabbitMQ.publishEvent(
          "subscription.events",
          "course.enrolled",
          courseEnrollmentEvent
        ),
      ]);

      console.log(
        `Payment completed and subscription created for student ${payment.studentId}`
      );
    } catch (error) {
      console.error("Error handling payment completion:", error);
    }
  }

  private async handlePaymentFailed(webhookPayload: any) {
    try {
      const xenditPaymentId = webhookPayload.data.id;

      // Find and update payment record
      const payment = await PaymentModel.findOneAndUpdate(
        { xenditPaymentId },
        {
          status: "failed",
          webhookData: webhookPayload,
        },
        { new: true }
      );

      if (payment) {
        console.log(
          `Payment failed for student ${payment.studentId}, payment ID: ${payment._id}`
        );
      }
    } catch (error) {
      console.error("Error handling payment failure:", error);
    }
  }
}
