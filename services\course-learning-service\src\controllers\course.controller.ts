import { Request, Response } from "express";
import { CourseModel } from "../models/Course.model";
import { AuthenticatedRequest } from "@shared/middleware/auth";
import { ApiResponse, PaginatedResponse } from "@shared/types";
import Joi from "joi";

export class CourseController {
  private createCourseSchema = Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(10).max(2000).required(),
    category: Joi.string().required(),
    duration: Joi.number().min(1).max(365).required(), // days
    price: Joi.number().min(0).required(),
    currency: Joi.string().default("IDR"),
  });

  async createCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { error, value } = this.createCourseSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const course = new CourseModel({
        ...value,
        tutorId: req.user!.uid,
        modules: [],
        isPublished: false,
      });

      await course.save();

      return res.status(201).json({
        success: true,
        message: "Course created successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Create course error:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to create course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getAllCourses(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 12;
      const category = req.query.category as string;
      const search = req.query.search as string;
      const sortBy = (req.query.sortBy as string) || "createdAt";
      const sortOrder = (req.query.sortOrder as string) || "desc";

      const filter: any = { isPublished: true };

      if (category) {
        filter.category = category;
      }

      if (search) {
        filter.$text = { $search: search };
      }

      const skip = (page - 1) * limit;
      const sort: any = {};
      sort[sortBy] = sortOrder === "desc" ? -1 : 1;

      const [courses, total] = await Promise.all([
        CourseModel.find(filter)
          .select("-modules") // Don't include full module data in catalog
          .skip(skip)
          .limit(limit)
          .sort(sort),
        CourseModel.countDocuments(filter),
      ]);

      const response: PaginatedResponse<(typeof courses)[0]> = {
        success: true,
        message: "Courses retrieved successfully",
        data: courses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      res.json(response);
    } catch (error: any) {
      console.error("Get all courses error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve courses",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getCourseById(req: Request, res: Response) {
    try {
      const course = await CourseModel.findById(req.params.id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found",
        } as ApiResponse);
      }

      // Only show full course details if published (or if user is tutor/admin)
      if (!course.isPublished) {
        return res.status(403).json({
          success: false,
          message: "Course not available",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course retrieved successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get course by ID error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async updateCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const { error, value } = this.createCourseSchema.validate(req.body, {
        allowUnknown: true,
      });
      if (error) {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: error.details.map((detail) => detail.message),
        } as ApiResponse);
      }

      const course = await CourseModel.findOneAndUpdate(
        {
          _id: req.params.id,
          tutorId: req.user!.uid, // Only tutor can update their own course
        },
        { $set: value },
        { new: true, runValidators: true }
      );

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course updated successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Update course error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async deleteCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const course = await CourseModel.findOneAndDelete({
        _id: req.params.id,
        tutorId: req.user!.uid, // Only tutor can delete their own course
      });

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course deleted successfully",
      } as ApiResponse);
    } catch (error: any) {
      console.error("Delete course error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async publishCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const course = await CourseModel.findOneAndUpdate(
        {
          _id: req.params.id,
          tutorId: req.user!.uid,
        },
        { $set: { isPublished: true } },
        { new: true }
      );

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      // Validate course has content before publishing
      if (!course.modules || course.modules.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Cannot publish course without modules",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course published successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Publish course error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to publish course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async unpublishCourse(req: AuthenticatedRequest, res: Response) {
    try {
      const course = await CourseModel.findOneAndUpdate(
        {
          _id: req.params.id,
          tutorId: req.user!.uid,
        },
        { $set: { isPublished: false } },
        { new: true }
      );

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found or unauthorized",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course unpublished successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Unpublish course error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to unpublish course",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getTutorCourses(req: AuthenticatedRequest, res: Response) {
    try {
      const courses = await CourseModel.find({ tutorId: req.user!.uid }).sort({
        createdAt: -1,
      });

      res.json({
        success: true,
        message: "Tutor courses retrieved successfully",
        data: courses,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Get tutor courses error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve tutor courses",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async getAllCoursesAdmin(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const skip = (page - 1) * limit;

      const [courses, total] = await Promise.all([
        CourseModel.find().skip(skip).limit(limit).sort({ createdAt: -1 }),
        CourseModel.countDocuments(),
      ]);

      const response: PaginatedResponse<(typeof courses)[0]> = {
        success: true,
        message: "All courses retrieved successfully",
        data: courses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      res.json(response);
    } catch (error: any) {
      console.error("Get all courses admin error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve courses",
        errors: [error.message],
      } as ApiResponse);
    }
  }

  async approveCourse(req: Request, res: Response) {
    try {
      const course = await CourseModel.findByIdAndUpdate(
        req.params.id,
        { $set: { isPublished: true } },
        { new: true }
      );

      if (!course) {
        return res.status(404).json({
          success: false,
          message: "Course not found",
        } as ApiResponse);
      }

      res.json({
        success: true,
        message: "Course approved and published successfully",
        data: course,
      } as ApiResponse);
    } catch (error: any) {
      console.error("Approve course error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to approve course",
        errors: [error.message],
      } as ApiResponse);
    }
  }
}
